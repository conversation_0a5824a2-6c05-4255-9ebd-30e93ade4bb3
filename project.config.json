{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.10.0", "packOptions": {"ignore": [], "include": []}, "appid": "wx2644979d13d1749f"}