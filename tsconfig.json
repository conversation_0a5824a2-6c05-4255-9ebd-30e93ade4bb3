{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["ES2020"], "typeRoots": ["./typings"]}, "include": ["./**/*.ts", "miniprogram/pages/xr-template-markerLock/index.js", "miniprogram/pages/xr-markerPosition-recorder/index.js", "miniprogram/components/xr-markerPosition-recorder/index.js", "miniprogram/components/xr-marker-mode/index.js", "miniprogram/components/xr-agent-chat-scene/index.js", "miniprogram/components/xr-bottom-debug-info/index.js"]}