<xr-scene ar-system="modes:Plane; planeMode: 1" bind:ready="handleReady" bind:ar-ready="handleARReady">
  <!-- vio + marker 模式下 planeMode 需设置为 1 (只允许水平面识别) -->
  <xr-assets>
    <xr-asset-load type="gltf" asset-id="anchor" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/ar-plane-marker.glb" />
  </xr-assets>
  <xr-node>

    <!-- plane -->
    <xr-ar-tracker id="plane" mode="Plane">
      <xr-gltf model="anchor"></xr-gltf>
      <!-- <xr-asset-material asset-id="simple" effect="simple" uniforms="u_baseColorFactor:0.8 0.4 0.4 1" />
      <xr-mesh node-id="cube" geometry="cube" material="simple" /> -->
    </xr-ar-tracker>

    <!-- marker -->
    <!-- <xr-ar-tracker id="lockTracker" mode="Marker" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/marker/2dmarker-test.jpg">
      <xr-node id="lockItem">
        <xr-gltf model="butterfly" anim-autoplay position="0.2 0 -0.2" scale="0.6 0.6 0.6" rotation="0 -50 0" />
        <xr-gltf model="butterfly" anim-autoplay position="0.4 0 0.3" scale="0.5 0.5 0.5" rotation="0 -50 0" />
        <xr-gltf model="butterfly" anim-autoplay position="-0.3 0 0.3" scale="0.4 0.4 0.4" rotation="0 -50 0" />
      </xr-node>
    </xr-ar-tracker> -->

    <!-- 识别成功后放置的世界位置 -->
    <!-- marker 会动态创建并放在root下 -->

    <!-- <xr-node>
      <xr-ar-tracker
        mode="Marker"
        wx:if="{{arReady}}"
        wx:for="{{markerList}}" wx:for-item="markerItem"
        wx:key="{{markerItem.id}}"
        wx:if="{{markerItem.isActive && markerItem.id === 'entryMarker'}}"
        id="marker-{{markerItem.id}}"
        src="{{markerItem.markerImg}}"
        bind:ar-tracker-state="handleTrackerState"
      >
      </xr-ar-tracker>
    </xr-node> -->

    <xr-node node-id="ar-camera">
      <xr-camera
      id="camera" clear-color="0.925 0.925 0.925 1"
      background="ar" is-ar-camera
    ></xr-camera>
    </xr-node>
  </xr-node>
  <xr-node node-id="lights">
    <xr-light type="ambient" color="1 1 1" intensity="1" />
    <xr-light type="directional" rotation="180 0 0" color="1 1 1" intensity="3" />
  </xr-node>
</xr-scene>
