const xr = wx.getXrFrameSystem();
let waiting = false;
Component({
  properties: {
    markerListRaw: {
      type: Array,
      value: []
    },
    isEntryMarkerTrackedRaw: Boolean
  },
  data: {
    loaded: true,
    arReady: false,
    markerList: [],
    isEntryMarkerTracked: false
  },
  lifetimes: {
    async attached() {
      console.log('data', this.data)
    }
  },
  observers: {
    markerListRaw(newVal) {
      this.setData({
        markerList: newVal,
      })
    },
    isEntryMarkerTrackedRaw(newVal) {
      this.setData({
        isEntryMarkerTracked: newVal
      })
    }
  },
  methods: {
    handleReady({detail}) {
      const xrScene = this.scene = detail.value;
      this.mat = new (wx.getXrFrameSystem().Matrix4)();
      console.log('xr-scene', xrScene);
    },
    handleARReady: async function({detail}) {
      console.log('arReady', this.scene.ar.arVersion);
      this.setData({ arReady: true })
    },
    getPlaneTrackerTransform(trackerId) {
      // 从 tracker 获取 Transform 组件
      const planeTrackerTransform = this.scene.getElementById('plane').getComponent(xr.Transform)
      if (planeTrackerTransform) {
        // 获取 Transform 的位置、旋转
        const position = {
          x: planeTrackerTransform.position.x,
          y: planeTrackerTransform.position.y,
          z: planeTrackerTransform.position.z
        }
        const euler = planeTrackerTransform.rotation
        const rotation = {
          x: euler.x,
          y: euler.y,
          z: euler.z
        };
        console.log('Position:', position);
        console.log('Rotation:', rotation);
        return {position, rotation}
      } else {
        console.warn("Transform component not found on tracker.");
        return null
      }
    },
    handleTrackerState({detail}) {
      console.log('handleTrackerState')
      // 获取当前状态和错误信息
      const tracker = detail.value
      const {state, errorMessage} = tracker;

      if (state === 2) {
        console.log('match')
        this.recordPlaneTrackerTransform(tracker.el.id.substring(7))
    }
    },
    recordPlaneTrackerTransform(itemId) {
      const trackerTrs = this.getPlaneTrackerTransform()
      this.triggerEvent('trackerPositionReceived', {
        id: itemId,
        position: trackerTrs.position,
        rotation: trackerTrs.rotation
      })
    }
  }
})