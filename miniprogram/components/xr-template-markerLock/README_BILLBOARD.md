# XRText Billboard 功能说明

## 功能概述

为XRText组件添加了billboard效果，使文字始终面向相机，提供更好的可读性和用户体验。

## 实现原理

### 1. 文字节点标识
- 为每个XRText节点添加唯一ID：`text-${marker.id}`
- 便于在更新循环中识别和操作文字节点

### 2. Billboard更新逻辑
在`handleOnTick`方法中调用`updateTextBillboard()`方法，实现以下步骤：

1. **获取相机位置**：从`this.cameraTrs.position`获取当前相机位置
2. **遍历所有文字节点**：查找所有以`lockItem-`开头的节点及其文字子节点
3. **计算朝向**：
   - 计算从文字位置到相机位置的方向向量
   - 使用`Math.atan2(dirX, dirZ)`计算Y轴旋转角度
4. **更新旋转**：设置文字节点的旋转为`(0, yRotation, Math.PI)`
   - X轴旋转：0（保持水平）
   - Y轴旋转：面向相机的角度
   - Z轴旋转：π（180度翻转，确保文字正向显示）

### 3. 性能优化
- 每60帧（约1秒）输出一次调试信息，避免控制台日志过多
- 使用try-catch包装，确保错误不会影响主要功能

## 代码修改

### 1. XRText创建时添加ID
```javascript
const xrText = scene.createElement(xr.XRText, {
  // ... 其他属性
  id: `text-${marker.id}` // 添加ID以便后续更新
})
```

### 2. 在tick循环中调用更新
```javascript
handleOnTick(deltaTime) {
  // ... 现有逻辑
  
  // 更新所有文字的billboard效果
  this.updateTextBillboard()
}
```

### 3. Billboard更新方法
```javascript
updateTextBillboard() {
  // 获取相机位置，计算文字朝向，更新旋转
}
```

## 使用效果

- 文字标签始终面向用户，无论用户从哪个角度观看
- 提高文字可读性，特别是在用户移动时
- 保持文字的正确朝向，避免倒置或侧向显示

## 调试信息

控制台会定期输出更新的文字节点数量：
```
[Billboard] Updated 3 text nodes, total children: 5
[Billboard] Camera position: (1.23, 0.45, -2.67)
```

这有助于确认功能正常工作并监控性能。

## 错误处理

代码包含了完善的错误处理：
- 检查必要的组件是否存在（root、cameraTrs）
- 验证Transform组件和worldPosition属性
- 避免除零错误（当相机和文字位置过于接近时）
- 使用try-catch包装，确保错误不会影响主要功能

## 测试建议

1. **基本功能测试**：
   - 创建包含文字的marker
   - 移动设备观察文字是否始终面向相机
   - 检查控制台是否有billboard更新日志

2. **边界情况测试**：
   - 相机非常接近文字时的表现
   - 多个文字节点同时存在时的性能
   - AR跟踪丢失和恢复时的稳定性

3. **性能测试**：
   - 观察帧率是否受到影响
   - 监控内存使用情况
   - 测试长时间运行的稳定性
