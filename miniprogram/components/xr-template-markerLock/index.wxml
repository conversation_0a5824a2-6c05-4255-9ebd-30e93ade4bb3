<xr-scene ar-system="modes:Plane; planeMode: 1;" bind:ready="handleReady" bind:ar-ready="handleARReady">
  <!-- vio + marker 模式下 planeMode 需设置为 1 (只允许水平面识别) -->
  <xr-assets>
    <xr-asset-load type="gltf" asset-id="anchor" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/ar-plane-marker.glb" />
    <xr-asset-load type="gltf" asset-id="agent" src="/assets/models/RobotExpressive.glb" />
    <xr-asset-load type="texture" asset-id="planeTexture" src="/assets/image/grid_texture.png" options="wrapU:1,wrapV:2" />
    <xr-asset-material asset-id="standard-mat" effect="standard" />
    <!-- <xr-asset-load type="gltf" asset-id="agent" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/miku.glb" /> -->
  </xr-assets>
  <xr-node>

    <!-- plane -->
    <!-- <xr-ar-tracker id='plane' mode="Plane">
      <xr-gltf model="anchor"></xr-gltf>
    </xr-ar-tracker> -->

  <!-- <xr-node id="meshOrigin">
    <xr-mesh id="mesh-x" node-id="mesh-x" position="0.25 0 0"  scale="0.5 0.02 0.02" geometry="cube" uniforms="u_baseColorFactor:0.7 0.3 0.3 1" ></xr-mesh>
    <xr-mesh id="mesh-y" node-id="mesh-y" position="0 0.25 0"  scale="0.02 0.5 0.02" geometry="cube" uniforms="u_baseColorFactor:0.3 0.7 0.3 1"></xr-mesh>
    <xr-mesh id="mesh-z" node-id="mesh-z" position="0 0 0.25"  scale="0.02 0.02 0.5" geometry="cube" uniforms="u_baseColorFactor:0.3 0.3 0.7 1"></xr-mesh>
  </xr-node> -->

    <!-- marker -->
    <!-- <xr-ar-tracker id="lockTracker" mode="Marker" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/marker/2dmarker-test.jpg">
      <xr-node id="lockItem">
        <xr-gltf model="butterfly" anim-autoplay position="0.2 0 -0.2" scale="0.6 0.6 0.6" rotation="0 -50 0" />
        <xr-gltf model="butterfly" anim-autoplay position="0.4 0 0.3" scale="0.5 0.5 0.5" rotation="0 -50 0" />
        <xr-gltf model="butterfly" anim-autoplay position="-0.3 0 0.3" scale="0.4 0.4 0.4" rotation="0 -50 0" />
      </xr-node>
    </xr-ar-tracker> -->

    <!-- 识别成功后放置的世界位置 -->
    <!-- marker 会动态创建并放在root下 -->

    <!-- <xr-node wx:if="{{arReady}}">
    <xr-node wx:if="{{isMarkerTrackerOn}}">
      <xr-ar-tracker
        mode="Marker"
        wx:for="{{activeAnchorList}}" wx:for-item="anchorItem"
        wx:key="{{anchorItem.id}}"
        id="anchor-{{anchorItem.id}}"
        src="{{anchorItem.anchorImg}}"
        bind:ar-tracker-state="handleTrackerState"
      >
      <xr-node position="0.5 0 0" id="normal-right-{{anchorItem.id}}"></xr-node>
      <xr-shadow id="root"></xr-shadow>
      </xr-ar-tracker>
    </xr-node>
    </xr-node> -->

  <xr-node>
    <xr-shadow id="root"></xr-shadow>
  </xr-node>

    <xr-node node-id='ar-camera'>
      <xr-camera
      id="camera" clear-color="0.925 0.925 0.925 1"
      background="ar" is-ar-camera
    >
    <!-- <xr-gltf model="agent" position="-0.65 -0.8 3" scale="0.2 0.2 -0.2" rotation="0 0 0" anim-autoplay /> -->
    </xr-camera>
    </xr-node>
  </xr-node>
  <xr-node node-id="lights">
    <xr-light type="ambient" color="1 1 1" intensity="1" />
    <xr-light type="directional" rotation="180 0 0" color="1 1 1" intensity="3" />
  </xr-node>
</xr-scene>
