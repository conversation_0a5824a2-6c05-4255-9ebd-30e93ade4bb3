const xr = wx.getXrFrameSystem();

const CameraState = {
  CAMERA_INIT: 0,
  CAMERA_TRACKING: 1,
  CAMERA_UNTRACKING: 2
}

function CameraStateManager() {
  let detectingCount = 0
  let detectingPos = null
  let state = -1

  this.getCameraState = (prev, curr) => {
    if (curr.equal(xr.Vector3.ZERO)) {
      state = CameraState.CAMERA_INIT
    } else if (prev.equal(curr)) {
      if (state === CameraState.CAMERA_UNTRACKING) return state
      if (detectingPos) {
        if (detectingPos.equal(curr)) {
          if (detectingCount >= 10) {
            //console.log('detectingCount: '+detectingCount)
            state = CameraState.CAMERA_UNTRACKING
            detectingCount = 0
            return state
          }
          detectingCount += 1
        } else {
          detectingPos.setValue(curr.x, curr.y, curr.z)
          detectingCount = 1
        }
      } else {
        detectingPos = curr
        detectingCount = 1
      }
      state = CameraState.CAMERA_TRACKING
    } else {
      state = CameraState.CAMERA_TRACKING
      detectingCount = 0
      detectingPos = null
    }
    return state
}
}

module.exports = CameraStateManager