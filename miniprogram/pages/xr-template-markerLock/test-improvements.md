# 展项进入/离开判断机制改进测试

## 改进内容

### 1. 离开展项判断机制优化
- **原逻辑**: 仅基于距离判断（距离 >= 1.8m 即触发离开）
- **新逻辑**: 优先考虑朝向，再考虑距离
  - **背对离开**: 用户背对展项（角度 > 120°）时，即使距离较近也触发离开
  - **距离离开**: 距离超过阈值且不面向展项（角度 > 60°）时触发离开
  - **智能判断**: 更符合用户交互习惯，避免误触发

### 2. 防频繁弹窗机制
- **弹窗冷却**: 同一展项10秒内不重复弹窗
- **用户拒绝冷却**: 用户拒绝后30秒内不再弹窗
- **弹窗状态锁**: 有弹窗显示时完全停止距离检测，防止重复弹窗
- **自动清理**: 定期清理过期记录，避免内存泄漏

### 3. 展项状态管理
- **状态跟踪**: 记录每个展项的访问状态（未看/正在看/已看）
- **防重复访问**: 已访问的展项不会再次触发进入弹窗
- **状态重置**: 提供重置功能用于测试或特殊情况

## 测试场景

### 场景1: 离开展项判断
1. 用户进入展项范围并确认体验
2. 用户在展项附近移动（距离可能超过1.8m但仍面向展项，角度 < 60°）
   - **预期**: 不触发离开弹窗
3. 用户背对展项（角度 > 120°）
   - **预期**: 立即触发离开弹窗，无需等待距离超过阈值
4. 用户走远但仍侧向展项（距离 > 1.8m，角度在60°-120°之间）
   - **预期**: 触发离开弹窗

### 场景2: 防频繁弹窗
1. 用户接近展项，触发进入弹窗
2. 用户点击"稍后再说"拒绝进入
3. 用户在30秒内再次接近同一展项
   - **预期**: 不再弹窗
4. 30秒后用户再次接近
   - **预期**: 重新弹窗

### 场景3: 弹窗冷却
1. 用户接近展项，触发弹窗
2. 用户离开范围，弹窗消失
3. 用户在10秒内再次接近
   - **预期**: 不弹窗
4. 10秒后再次接近
   - **预期**: 重新弹窗

### 场景4: 展项状态管理
1. 用户首次接近展项A
   - **预期**: 触发进入弹窗
2. 用户确认进入并体验，然后离开
   - **预期**: 展项A状态变为"已看"
3. 用户再次接近展项A
   - **预期**: 不再触发进入弹窗（因为已访问过）
4. 调用重置方法后再次接近
   - **预期**: 重新触发进入弹窗

### 场景5: 防重复弹窗
1. 用户接近展项，触发弹窗
2. 弹窗显示期间，用户继续移动（满足触发条件）
   - **预期**: 不会出现第二个弹窗
3. 用户点击弹窗按钮后
   - **预期**: 弹窗状态锁解除，恢复正常检测

## 关键参数

```javascript
const MODAL_COOLDOWN = 10000      // 弹窗冷却时间10秒
const REJECTION_COOLDOWN = 30000  // 用户拒绝后冷却时间30秒
const distanceThreshold = 1.8     // 距离阈值1.8米
const backToMarkerAngle = 120     // 背对展项角度阈值120度
const facingMarkerAngle = 60      // 面向展项角度阈值60度
```

## 展项状态说明

```javascript
// 展项访问状态
markerVisitStatus = {
  'marker1': 'unvisited',  // 未访问
  'marker2': 'visiting',   // 正在访问
  'marker3': 'visited'     // 已访问
}
```

## 调试信息

系统会在控制台输出以下调试信息：
- 相机朝向检测角度和距离
- 离开检测的角度、距离、背对状态、面向状态和最终判断
- 弹窗冷却期跳过信息
- 用户拒绝冷却期跳过信息
- 展项状态变更信息

## 管理方法

```javascript
// 重置特定展项状态
this.resetMarkerVisitStatus('marker1')

// 重置所有展项状态
this.resetMarkerVisitStatus()

// 获取展项状态
const status = this.getMarkerVisitStatus('marker1')

// 获取所有展项状态
const allStatus = this.getAllMarkerVisitStatus()
```

## 注意事项

1. 确保相机朝向数据（cameraQuat）正常获取
2. 如果朝向数据异常，系统会回退到仅使用距离判断
3. 用户主动进入展项时会清除该展项的拒绝记录
4. 页面卸载时会清理所有防频繁弹窗记录
