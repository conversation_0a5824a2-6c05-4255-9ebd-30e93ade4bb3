# 改进后的展项判断机制使用示例

## 核心改进总结

### 1. 智能离开判断
现在的离开判断逻辑更加智能：

```javascript
// 原来：只看距离
if (distance >= 1.8) {
  // 触发离开
}

// 现在：优先看朝向，再看距离
isLeavingMarker(markerPos, cameraPos, cameraQuat, distance) {
  // 1. 背对展项（角度 > 120°）→ 立即离开
  const isBackToMarker = actualAngle > 120;
  
  // 2. 距离远且不面向（距离 > 1.8m 且角度 > 60°）→ 离开
  const isFacingMarker = actualAngle < 60;
  const isLeavingByDistance = distance >= 1.8 && !isFacingMarker;
  
  return isBackToMarker || isLeavingByDistance;
}
```

### 2. 展项状态管理
```javascript
// 展项状态：'unvisited' | 'visiting' | 'visited'
markerVisitStatus = {
  'marker1': 'visited',    // 已看过，不会再弹窗
  'marker2': 'visiting',   // 正在看
  'marker3': 'unvisited'   // 未看过
}
```

### 3. 防频繁弹窗
```javascript
// 多重保护机制
if (visitStatus !== 'visited' && this.shouldShowEnterModal(marker.id)) {
  // 只有未访问过且通过频繁检查的才弹窗
}
```

## 实际使用场景

### 场景A：用户在展项附近正常移动
```
用户位置：展项前方1.5米
用户朝向：面向展项（角度30°）
距离判断：< 1.8米 ✓
朝向判断：面向展项 ✓
结果：不触发离开，用户可以正常观看
```

### 场景B：用户明确要离开
```
用户位置：展项后方
用户朝向：背对展项（角度150°）
距离判断：可能 < 1.8米
朝向判断：背对展项 ✗
结果：立即触发离开弹窗
```

### 场景C：用户走远但还在关注
```
用户位置：展项侧方2.5米
用户朝向：侧向展项（角度45°）
距离判断：> 1.8米 ✗
朝向判断：仍面向展项 ✓
结果：不触发离开，用户可能还在观看
```

### 场景D：用户走远且不再关注
```
用户位置：展项侧方2.5米
用户朝向：侧向其他方向（角度80°）
距离判断：> 1.8米 ✗
朝向判断：不面向展项 ✗
结果：触发离开弹窗
```

## 测试建议

### 1. 基础功能测试
```javascript
// 在控制台查看调试信息
console.log('离开检测 - 角度: 45.0°, 距离: 2.50m, 背对: false, 面向: true, 离开: false')

// 查看展项状态
console.log(this.getAllMarkerVisitStatus())
```

### 2. 重置功能测试
```javascript
// 重置特定展项（用于重复测试）
this.resetMarkerVisitStatus('marker1')

// 重置所有展项
this.resetMarkerVisitStatus()
```

### 3. 边界情况测试
- 朝向数据异常时的降级处理
- 距离极近时的特殊处理
- 长时间使用后的内存表现

## 参数调优

如需调整判断灵敏度，可修改以下参数：

```javascript
// 在 isLeavingMarker 方法中
const isBackToMarker = actualAngle > 120;  // 背对角度阈值
const isFacingMarker = actualAngle < 60;   // 面向角度阈值

// 在全局变量中
const distanceThreshold = 1.8;             // 距离阈值
const MODAL_COOLDOWN = 10000;              // 弹窗冷却时间
const REJECTION_COOLDOWN = 30000;          // 拒绝后冷却时间
```

## 注意事项

1. **朝向数据依赖**：确保 `cameraQuat` 数据正常，异常时会降级到距离判断
2. **状态持久化**：当前状态仅在内存中，页面刷新会重置
3. **调试信息**：生产环境可考虑关闭详细的 console.log 输出
4. **性能考虑**：每3秒执行一次检测，对性能影响较小
