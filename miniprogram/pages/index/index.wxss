.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
}

/* 标题样式 */
.title {
  top: 10%;
  position: absolute;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.tabs {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

button {
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
}

.bottom-ui {
  position: absolute;
  bottom: 10%
}

/* 图片样式 */
.robot-image {
  width: 150px;
  height: auto;
  margin-bottom: 20%;
}

/* 说明文字样式 */
.description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

/* 按钮样式 */
.start-button {
  background-color: #007aff;
  color: #fff;
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 5px;
}
