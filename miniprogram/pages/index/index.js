Page({
  data: {
    // 页面数据可以在此定义
  },
  onLoad() {
    //this.navigateToAgentChat()
  },
  navigateToRecorder() {
    wx.navigateTo({
      url: '/pages/xr-markerPosition-recorder/index'
    });
  },
  navigateToTracker() {
    wx.navigateTo({
      url: '/pages/xr-template-tracker/index'
    });
  },
  navigateToMarkerLock() {
    wx.navigateTo({
      url: '/pages/xr-template-markerLock/index'
    });
  },
  navigateToAgentChat() {
    wx.navigateTo({
      url: '/pages/xr-agent-chat/index'
    })
  }
});
