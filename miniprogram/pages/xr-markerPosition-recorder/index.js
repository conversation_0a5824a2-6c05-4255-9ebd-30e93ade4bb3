var sceneReadyBehavior = require('../behavior-scene/scene-ready');
var mockDataList = require('../../data/index').mockMarkerDataList
var AnchorPose = require('../../data/models').AnchorPose

const xr = wx.getXrFrameSystem();

Page({
  behaviors:[sceneReadyBehavior],
  data: {
    markerList: [],
    activeMarker: null,
    dataReady: false,
    showBackBtn: true,
    scannedMarkerList: [],
    isEntryMarkerTracked: false
  },
  onLoad() {
    const activeMarker = mockDataList.find(elem => elem.isActive)
    this.setData({
      markerList: mockDataList,
      activeMarker: activeMarker
    })
  },
  resetData() {
    this.setData({
      dataReady: false
    })
  },
  recordPose(cur) {
    const recorderComponent = this.selectComponent('#main-frame')
    if (recorderComponent) {
      if (!this.data.activeMarker) {
        console.log('需选中一个展项再打点')
        return
      }
      recorderComponent.recordPlaneTrackerTransform(this.data.activeMarker.id)
    } else {
      console.log('Cannot find recorder component in the page')
    }
  },
  handleTrackerPositionReceived(cur) {
    try {
      const item = cur.detail
      const markerPose = new AnchorPose(item.id, item.position, item.rotation)
      const scannedMarkerList = this.data.scannedMarkerList
      if (scannedMarkerList.length > 0) {
        var previousMarkerPose = scannedMarkerList[scannedMarkerList.length-1]
        var distance = this.calculateDistance(markerPose.position, previousMarkerPose.position)
        console.log('距离上一个marker：'+distance)
        wx.showToast({
          title: '距离：'+distance,
        })
      } else {
        wx.showToast({
          title: '初始定位成功',
        })
        this.setData({
          isEntryMarkerTracked: true
        });
      }
      scannedMarkerList.push(markerPose)
      this.setData({
        scannedMarkerList: scannedMarkerList
      })
      wx.setStorageSync(item.id, markerPose);
      console.log(item.id+"的数据存储成功: position: "+wx.getStorageSync(item.id).position+'rotation: '+wx.getStorageSync(item.id).rotation);
    } catch (e) {
      console.error("数据存储失败", e);
    }
  },
  // 计算三维空间距离的方法
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  },
  toggleActiveness(e) {
    const { key } = e.currentTarget.dataset;
    const markerList = this.data.markerList.map(item => ({
      ...item,
      isActive: item.id === key ? true : false
    }));
    const activeMarker = markerList.find(elem => elem.isActive)
    this.setData({
      markerList: markerList,
      activeMarker: activeMarker
    });
  }
});