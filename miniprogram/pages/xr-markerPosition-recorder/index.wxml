<view>
  <xr-markerPosition-recorder
    disable-scroll
    id="main-frame"
    width="{{renderWidth}}"
    height="{{renderHeight}}"
    style="width:{{width}}px;height:{{height}}px;top:{{top}}px;left:{{left}}px;display:block;"
    markerListRaw="{{markerList}}"
    isEntryMarkerTrackedRaw="{{isEntryMarkerTracked}}"
    bindtrackerPositionReceived="handleTrackerPositionReceived"
  />

  <!-- Marker控制 -->
  <view class="bottom-ui">
    <button bindtap="recordPose">打点</button>
    <scroll-view class="xr-control" scroll-x="true"
    scroll-with-animation="true"
    style="white-space: nowrap; top: {{height}}px;">
      <view wx:for="{{markerList}}" wx:key="id" class="control-item {{item.isActive ? 'active' : ''}}" data-key="{{item.id}}" bind:tap="toggleActiveness">
      <view class="item-title">{{item.name}}</view>
      <view wx:if="{{item.anchorImg}}" class="img-wrap">
        <image class="hint-img" src="{{item.anchorImg}}" />
      </view>
    </view>
    </scroll-view>
  </view>
</view>