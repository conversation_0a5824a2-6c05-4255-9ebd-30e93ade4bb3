const mockMarkerDataList = [
  {
    id: 'entryMarker',
    name: '入场',
    isActive: true
  },
  {
    id: '1',
    name: '格尔尼卡',
    anchorImg: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/marker/2dmarker-test.jpg',
    type: 'gltf',
    src: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/butterfly/index.glb',
    worldPoses: [
      {
        position: '0.2 0 -0.2', scale: '0.6 0.6 0.6', rotation: '0 -50 0'
      }, 
      {
        position: '0.4 0 0.3', scale: '0.5 0.5 0.5', rotation: '0 -50 0'
      },
      {
        position: '-0.3 0 0.3', scale: '0.4 0.4 0.4', rotation: '0 -50 0'
      }
    ],
  isActive: false
  },
  {
    id: '2',
    name: '睡莲',
    anchorImg: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/portalImage.jpg',
    type: 'gltf',
    src: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/fiesta_tea/scene.gltf',
    worldPoses: [
    {
      position: '0 -1.6 1',
      scale: '0.6 0.6 0.6',
      rotation: '-90 0 0'
    }
  ],
  isActive: false
  },
  {
    id: '3',
    name: '星月夜',
    anchorImg: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/wxball.jpg',
    type: 'gltf',
    src: 'https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/wxball/wxball.gltf',
    worldPoses: [
      {
        position: '0 -0.1 0', scale: '0.16 0.16 0.16', rotation: '-45 30 0'
      }
    ],
    isActive: false
  },
  {
    id: 'exitMarker',
    name: '离场',
    isActive: false
  },
]

const sensitiveWords = [
  '操作'
]

module.exports = {
  mockMarkerDataList,
  sensitiveWords
}