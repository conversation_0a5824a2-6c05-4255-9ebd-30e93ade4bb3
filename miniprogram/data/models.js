class AnchorPose {
  constructor(id, position, rotation) {
    this.id = id
    this.position = position
    this.rotation = rotation
  }
}

const itemsData = {
  '255': {
    'itemId': '9',
    'itemName': '华夏硅谷梦'
  },
  '256': {
    'itemId': '10',
    'itemName': '下海创业潮'
  },
  '257': {
    'itemId': '11',
    'itemName': '汉字信息化'
  },
  '259': {
    'itemId': '12',
    'itemName': '成立试验区'
  },
  '260': {
    'itemId': '13',
    'itemName': 'IT产业先锋'
  },
  '261': {
    'itemId': '14',
    'itemName': '互联网新世纪'
  }
}

class AnchorData {
  constructor(id, position, rotation, scale, url, assetId) {
    this.id = id
    this.position = {
      x: position.x,
      y: position.y,
      z: position.z
    }
    this.rotation = {
      x: rotation.x * 180 / Math.PI,
      y: rotation.y * 180 / Math.PI,
      z: rotation.z * 180 / Math.PI
    }
    this.scale = scale
    this.isActive = false
    this.assetId = assetId
    this.url = url
    const item = itemsData[this.id]
    if (item) {
      console.log('item data: '+JSON.stringify(item))
      this.exhibitItemId = item.itemId
      this.name = item.itemName
    } else {
      console.log('item is not found')
    }
  }
}

module.exports = {
  AnchorPose,
  AnchorData
}