class TimeoutError extends Error {
  constructor(message) {
    super(message);
    this.name = 'TimeoutError';
    this.type = 'TIMEOUT_ERROR';
  }
}


class HTTPError extends Error {
  constructor(statusCode, message, data) {
    super(message);
    this.name = 'HTTPError';
    this.type = 'HTTP_ERROR';
    this.statusCode = statusCode;
    this.data = data;
  }
}

class NetworkError extends Error {
  constructor(message, originalError) {
    super(message);
    this.name = 'NetworkError';
    this.type = 'NETWORK_ERROR';
    this.originalError = originalError;
  }
}

class ParseError extends Error {
  constructor(message, originalError) {
    super(message);
    this.name = 'ParseError';
    this.type = 'PARSE_ERROR';
    this.originalError = originalError;
  }
}

class UnsupportedRequestError extends Error {
  constructor(message) {
    super(message);
    this.name = 'UnsupportedRequestError';
    this.type = 'UNSUPPORTED_REQUEST_ERROR';
  }
}

module.exports = {
  TimeoutError,
  HTTPError,
  NetworkError,
  ParseError,
  UnsupportedRequestError
}